import { Injectable, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { Reflector } from '@nestjs/core';
import { IS_PUBLIC_KEY } from '../../core/decorators/auth.decorators';

/**
 * System JWT Authentication Guard
 * Protects routes that require system user authentication
 */
@Injectable()
export class SystemJwtAuthGuard extends AuthGuard('system-jwt') {
    constructor(private reflector: Reflector) {
        super();
    }

    canActivate(context: ExecutionContext) {
        console.log(`🔥 [DEBUG] SystemJwtAuthGuard.canActivate called`);

        const isPublic = this.reflector.getAllAndOverride<boolean>(IS_PUBLIC_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);

        console.log(`🔥 [DEBUG] SystemJwtAuthGuard isPublic: ${isPublic}`);

        if (isPublic) {
            console.log(`🔥 [DEBUG] SystemJwtAuthGuard allowing public access`);
            return true;
        }

        console.log(`🔥 [DEBUG] SystemJwtAuthGuard calling super.canActivate`);
        const result = super.canActivate(context);
        console.log(`🔥 [DEBUG] SystemJwtAuthGuard super.canActivate result:`, result);
        return result;
    }

    handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
        console.log(`🔥 [DEBUG] SystemJwtAuthGuard.handleRequest called`);
        console.log(`🔥 [DEBUG] SystemJwtAuthGuard err:`, err);
        console.log(`🔥 [DEBUG] SystemJwtAuthGuard user:`, user);
        console.log(`🔥 [DEBUG] SystemJwtAuthGuard info:`, info);

        if (err || !user) {
            console.log(`🔥 [DEBUG] SystemJwtAuthGuard throwing UnauthorizedException - no user or error`);
            throw err || new UnauthorizedException('System authentication required');
        }

        // Ensure this is a system user
        if (user.userType !== 'system') {
            console.log(`🔥 [DEBUG] SystemJwtAuthGuard throwing UnauthorizedException - not system user`);
            throw new UnauthorizedException('System user access required');
        }

        console.log(`🔥 [DEBUG] SystemJwtAuthGuard authentication successful`);
        return user;
    }
}
