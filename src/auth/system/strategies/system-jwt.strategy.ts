import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SystemUser } from '../entities/system-user.entity';
import { SystemUserJwtPayload, UserType } from '../../core/types/auth.types';

/**
 * System JWT Strategy
 * Validates JWT tokens for system users and injects user context
 */
@Injectable()
export class SystemJwtStrategy extends PassportStrategy(Strategy, 'system-jwt') {
    constructor(
        @InjectRepository(SystemUser)
        private systemUserRepository: Repository<SystemUser>,
        private configService: ConfigService,
    ) {
        super({
            jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get<string>('JWT_SECRET', 'default-secret'),
        });
    }

    async validate(payload: SystemUserJwtPayload): Promise<SystemUser> {
        console.log(`🔥 [DEBUG] SystemJwtStrategy.validate called with payload:`, payload);

        // Ensure this is a system user token
        if (payload.userType !== UserType.SYSTEM) {
            console.log(`🔥 [DEBUG] SystemJwtStrategy invalid userType: ${payload.userType}`);
            throw new UnauthorizedException('Invalid token type for system authentication');
        }

        console.log(`🔥 [DEBUG] SystemJwtStrategy finding user with ID: ${payload.sub}`);
        // Find user in database
        const user = await this.systemUserRepository.findOne({
            where: { id: payload.sub }
        });

        if (!user) {
            console.log(`🔥 [DEBUG] SystemJwtStrategy user not found for ID: ${payload.sub}`);
            throw new UnauthorizedException('System user not found');
        }

        if (!user.isActive) {
            console.log(`🔥 [DEBUG] SystemJwtStrategy user inactive for ID: ${payload.sub}`);
            throw new UnauthorizedException('System user account is inactive');
        }

        console.log(`🔥 [DEBUG] SystemJwtStrategy updating lastLoginAt for user: ${user.username}`);
        // Update last login timestamp
        user.lastLoginAt = new Date();

        console.log(`🔥 [DEBUG] SystemJwtStrategy SKIPPING database save to test...`);
        // await this.systemUserRepository.save(user);
        console.log(`🔥 [DEBUG] SystemJwtStrategy database save skipped`);

        // Add userType to user object for guards
        (user as any).userType = 'system';

        console.log(`🔥 [DEBUG] SystemJwtStrategy validation completed successfully for user: ${user.username}`);
        return user;
    }
}
