import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ConfigService } from '@nestjs/config';
import { Repository, In } from 'typeorm';
import axios from 'axios';

import { Player } from '../models/player.entity';
import { PlayerStatistics } from '../models/player-statistics.entity';
import {
    GetTopScorersDto,
    GetTopAssistsDto,
    GetPlayersDto,
    PaginatedPlayersResponse,
    PlayerResponseDto
} from '../models/player.dto';
import { CacheService } from '../../../core';
import { ImageService } from '../../../shared/services/image.service';

@Injectable()
export class PlayerService {
    private readonly logger = new Logger(PlayerService.name);
    private readonly BATCH_SIZE = 10; // Configurable batch size for processing
    private readonly MAX_RETRIES = 3; // Maximum retry attempts for API calls

    constructor(
        @InjectRepository(Player)
        private readonly playerRepository: Repository<Player>,
        @InjectRepository(PlayerStatistics)
        private readonly playerStatisticsRepository: Repository<PlayerStatistics>,
        private readonly configService: ConfigService,
        private readonly cacheService: CacheService,
        private readonly imageService: ImageService,
    ) { }

    /**
     * Get top scorers for a league and season with caching and database optimization
     *
     * @param query - Query parameters including league, season, page, and limit
     * @returns Promise<PaginatedPlayersResponse> - Paginated list of top scorers
     * @throws Error if validation fails or API/database errors occur
     *
     * @example
     * ```typescript
     * const topScorers = await playerService.getTopScorers({
     *   league: 39,
     *   season: 2024,
     *   page: 1,
     *   limit: 20
     * });
     * ```
     */
    async getTopScorers(query: GetTopScorersDto): Promise<PaginatedPlayersResponse> {
        const { league, season, page = 1, limit = 20 } = query;

        // Input validation
        this.validateLeagueAndSeason(league, season);
        this.validatePagination(page, limit);

        try {
            this.logger.debug(`Getting top scorers for league ${league}, season ${season}`);

            // Generate cache key
            const cacheKey = `topscorers_${league}_${season}_${page}_${limit}`;

            // Check cache first
            const cached = await this.cacheService.getCache(cacheKey);
            if (cached) {
                this.logger.debug(`Returning top scorers from cache for key: ${cacheKey}`);
                return JSON.parse(cached);
            }

            // Check database first - use QueryBuilder for complex JSONB queries
            const queryBuilder = this.playerRepository.createQueryBuilder('player')
                .leftJoinAndSelect('player.statistics', 'stats')
                .where('stats.leagueId = :leagueId', { leagueId: league })
                .andWhere('stats.season = :season', { season: season })
                .andWhere("stats.goals->>'total' IS NOT NULL")
                .andWhere("CAST(stats.goals->>'total' AS INTEGER) > 0")
                .orderBy("CAST(stats.goals->>'total' AS INTEGER)", 'DESC')
                .addOrderBy('player.name', 'ASC');

            let savedPlayers = await queryBuilder.getMany();

            // If no data in database, fetch from API
            if (savedPlayers.length === 0) {
                this.logger.debug(`No top scorers found in DB for league ${league}, season ${season}, fetching from API`);

                // Fetch and process from API (downloads images and saves to DB)
                const processedPlayers = await this.fetchTopScorersFromApi(league, season);

                // Save all players to database in parallel batches
                if (processedPlayers.length > 0) {
                    await this.savePlayersFromApiBatch(processedPlayers);
                    this.logger.debug(`Saved ${processedPlayers.length} top scorers to database`);

                    // Get external IDs to fetch from database
                    const externalIds = processedPlayers.map(apiData => apiData.player.id);

                    // Fetch saved players from database with local paths
                    savedPlayers = await this.playerRepository.find({
                        where: { externalId: In(externalIds) },
                        relations: ['statistics'],
                        order: { externalId: 'ASC' }
                    });
                }
            }

            // Map to response format using database data
            const mappedPlayers = savedPlayers.map(player => this.mapPlayerToResponse(player));

            // Apply pagination
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedPlayers = mappedPlayers.slice(startIndex, endIndex);

            const result: PaginatedPlayersResponse = {
                data: paginatedPlayers,
                meta: {
                    totalItems: mappedPlayers.length,
                    totalPages: Math.ceil(mappedPlayers.length / limit),
                    currentPage: page,
                    limit
                },
                status: 200
            };

            // Cache the result if we have data
            if (result.data.length > 0) {
                await this.cacheService.setCache(cacheKey, JSON.stringify(result), 3600); // 1 hour TTL
                this.logger.debug(`Cached top scorers for key: ${cacheKey}`);
            }

            return result;
        } catch (error) {
            this.logger.error(`Error in getTopScorers for league ${league}, season ${season}: ${error.message}`, error.stack);

            // Handle specific error types
            if (error.name === 'QueryFailedError') {
                throw new Error(`Database query failed for top scorers: ${error.message}`);
            }

            if (error.message?.includes('timeout')) {
                throw new Error(`Request timeout while fetching top scorers for league ${league}`);
            }

            if (error.response?.status === 429) {
                throw new Error('API rate limit exceeded. Please try again later.');
            }

            if (error.response?.status >= 500) {
                throw new Error('External API service temporarily unavailable. Please try again later.');
            }

            throw new Error(`Failed to get top scorers: ${error.message}`);
        }
    }

    /**
     * Get top assists for a league and season with caching and database optimization
     *
     * @param query - Query parameters including league, season, page, and limit
     * @returns Promise<PaginatedPlayersResponse> - Paginated list of top assist providers
     * @throws Error if validation fails or API/database errors occur
     *
     * @example
     * ```typescript
     * const topAssists = await playerService.getTopAssists({
     *   league: 39,
     *   season: 2024,
     *   page: 1,
     *   limit: 20
     * });
     * ```
     */
    async getTopAssists(query: GetTopAssistsDto): Promise<PaginatedPlayersResponse> {
        const { league, season, page = 1, limit = 20 } = query;

        // Input validation
        this.validateLeagueAndSeason(league, season);
        this.validatePagination(page, limit);

        try {
            this.logger.debug(`Getting top assists for league ${league}, season ${season}`);

            // Generate cache key
            const cacheKey = `topassists_${league}_${season}_${page}_${limit}`;

            // Check cache first
            const cached = await this.cacheService.getCache(cacheKey);
            if (cached) {
                this.logger.debug(`Returning top assists from cache for key: ${cacheKey}`);
                return JSON.parse(cached);
            }

            // Check database first - use QueryBuilder for complex JSONB queries
            const queryBuilder = this.playerRepository.createQueryBuilder('player')
                .leftJoinAndSelect('player.statistics', 'stats')
                .where('stats.leagueId = :leagueId', { leagueId: league })
                .andWhere('stats.season = :season', { season: season })
                .andWhere("stats.goals->>'assists' IS NOT NULL")
                .andWhere("CAST(stats.goals->>'assists' AS INTEGER) > 0")
                .orderBy("CAST(stats.goals->>'assists' AS INTEGER)", 'DESC')
                .addOrderBy('player.name', 'ASC');

            let savedPlayers = await queryBuilder.getMany();

            // If no data in database, fetch from API
            if (savedPlayers.length === 0) {
                this.logger.debug(`No top assists found in DB for league ${league}, season ${season}, fetching from API`);

                // Fetch and process from API (downloads images and saves to DB)
                const processedPlayers = await this.fetchTopAssistsFromApi(league, season);

                // Save all players to database in parallel batches
                if (processedPlayers.length > 0) {
                    await this.savePlayersFromApiBatch(processedPlayers);
                    this.logger.debug(`Saved ${processedPlayers.length} top assists to database`);

                    // Get external IDs to fetch from database
                    const externalIds = processedPlayers.map(apiData => apiData.player.id);

                    // Fetch saved players from database with local paths
                    savedPlayers = await this.playerRepository.find({
                        where: { externalId: In(externalIds) },
                        relations: ['statistics'],
                        order: { externalId: 'ASC' }
                    });
                }
            }

            // Map to response format using database data
            const mappedPlayers = savedPlayers.map(player => this.mapPlayerToResponse(player));

            // Apply pagination
            const startIndex = (page - 1) * limit;
            const endIndex = startIndex + limit;
            const paginatedPlayers = mappedPlayers.slice(startIndex, endIndex);

            const result: PaginatedPlayersResponse = {
                data: paginatedPlayers,
                meta: {
                    totalItems: mappedPlayers.length,
                    totalPages: Math.ceil(mappedPlayers.length / limit),
                    currentPage: page,
                    limit
                },
                status: 200
            };

            // Cache the result if we have data
            if (result.data.length > 0) {
                await this.cacheService.setCache(cacheKey, JSON.stringify(result), 3600); // 1 hour TTL
                this.logger.debug(`Cached top assists for key: ${cacheKey}`);
            }

            return result;
        } catch (error) {
            this.logger.error(`Error in getTopAssists for league ${league}, season ${season}: ${error.message}`, error.stack);

            // Handle specific error types
            if (error.name === 'QueryFailedError') {
                throw new Error(`Database query failed for top assists: ${error.message}`);
            }

            if (error.message?.includes('timeout')) {
                throw new Error(`Request timeout while fetching top assists for league ${league}`);
            }

            if (error.response?.status === 429) {
                throw new Error('API rate limit exceeded. Please try again later.');
            }

            if (error.response?.status >= 500) {
                throw new Error('External API service temporarily unavailable. Please try again later.');
            }

            throw new Error(`Failed to get top assists: ${error.message}`);
        }
    }

    /**
     * Search players
     */
    async searchPlayers(query: GetPlayersDto): Promise<PaginatedPlayersResponse> {
        try {
            const { search, page = 1, limit = 20 } = query;
            this.logger.debug(`Searching players with query: ${search}`);

            // Query database
            const qb = this.playerRepository.createQueryBuilder('player');

            if (search) {
                qb.andWhere('LOWER(player.name) LIKE LOWER(:search)', { search: `%${search}%` });
            }

            const totalItems = await qb.getCount();
            const players = await qb
                .skip((page - 1) * limit)
                .take(limit)
                .orderBy('player.name', 'ASC')
                .getMany();

            const result: PaginatedPlayersResponse = {
                data: players.map(player => this.mapPlayerToResponse(player)),
                meta: {
                    totalItems,
                    totalPages: Math.ceil(totalItems / limit),
                    currentPage: page,
                    limit
                },
                status: 200
            };

            return result;
        } catch (error) {
            this.logger.error(`Error in searchPlayers: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Get detailed player information by external ID
     *
     * @param externalId - The external API ID of the player
     * @param forceNewData - If true, force fetch from API and update database
     * @returns Promise<PlayerResponseDto> - Detailed player information with statistics
     * @throws Error if validation fails, player not found, or API/database errors occur
     *
     * @example
     * ```typescript
     * const player = await playerService.getPlayerById(276);
     * const freshPlayer = await playerService.getPlayerById(276, true); // Force API fetch
     * ```
     */
    async getPlayerById(externalId: number, forceNewData: boolean = false): Promise<PlayerResponseDto> {
        // Input validation
        this.validateExternalId(externalId);

        try {
            this.logger.debug(`Getting player by external ID: ${externalId}, forceNewData: ${forceNewData}`);

            // If forceNewData is true, fetch from API first and update database
            if (forceNewData) {
                this.logger.debug(`Force fetching player ${externalId} from API due to newdb=true`);

                const apiPlayer = await this.fetchPlayerFromApi(externalId);
                if (apiPlayer) {
                    // Save/update to database
                    const savedPlayer = await this.savePlayerFromApi(apiPlayer);
                    return this.mapPlayerToDetailedResponse(savedPlayer);
                }

                throw new NotFoundException(`Player with external ID ${externalId} not found in API`);
            }

            // Normal flow: Try database first
            const player = await this.playerRepository.findOne({
                where: { externalId },
                relations: ['statistics']
            });

            if (player) {
                return this.mapPlayerToDetailedResponse(player);
            }

            // If not found, fetch from API
            const apiPlayer = await this.fetchPlayerFromApi(externalId);
            if (apiPlayer) {
                // Save to database
                const savedPlayer = await this.savePlayerFromApi(apiPlayer);
                return this.mapPlayerToDetailedResponse(savedPlayer);
            }

            throw new NotFoundException(`Player with external ID ${externalId} not found`);
        } catch (error) {
            this.logger.error(`Error in getPlayerById: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Fetch top scorers from API
     */
    private async fetchTopScorersFromApi(league: number, season: number): Promise<any[]> {
        try {
            const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players/topscorers`;
            const apiKey = this.configService.get('app.apiFootballKey');

            this.logger.debug(`Fetching top scorers from API: ${apiUrl}?league=${league}&season=${season}`);

            const response = await axios.get(apiUrl, {
                headers: {
                    'x-apisports-key': apiKey,
                },
                params: {
                    league,
                    season,
                },
                timeout: 15000,
            });

            if (response.data && response.data.response) {
                this.logger.debug(`API returned ${response.data.response.length} top scorers`);
                return response.data.response;
            }

            this.logger.warn(`API returned no data for league ${league} season ${season}`);
            return [];
        } catch (error) {
            this.logger.error(`Error fetching top scorers from API: ${error.message}`);
            return [];
        }
    }

    /**
     * Fetch top assists from API
     */
    private async fetchTopAssistsFromApi(league: number, season: number): Promise<any[]> {
        try {
            const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players/topassists`;
            const apiKey = this.configService.get('app.apiFootballKey');

            this.logger.debug(`Fetching top assists from API: ${apiUrl}?league=${league}&season=${season}`);

            const response = await axios.get(apiUrl, {
                headers: {
                    'x-apisports-key': apiKey,
                },
                params: {
                    league,
                    season,
                },
                timeout: 15000,
            });

            if (response.data && response.data.response) {
                this.logger.debug(`API returned ${response.data.response.length} top assists`);
                return response.data.response;
            }

            this.logger.warn(`API returned no data for league ${league} season ${season}`);
            return [];
        } catch (error) {
            this.logger.error(`Error fetching top assists from API: ${error.message}`);
            return [];
        }
    }

    /**
     * Fetch single player from API
     */
    private async fetchPlayerFromApi(externalId: number): Promise<any> {
        try {
            const apiUrl = `${this.configService.get('app.apiFootballUrl')}/players`;
            const apiKey = this.configService.get('app.apiFootballKey');

            this.logger.debug(`Fetching player from API: ${apiUrl}?id=${externalId}`);

            const response = await axios.get(apiUrl, {
                headers: {
                    'x-apisports-key': apiKey,
                },
                params: {
                    id: externalId,
                    season: 2024,
                },
                timeout: 15000,
            });

            if (response.data && response.data.response && response.data.response.length > 0) {
                this.logger.debug(`API returned player data for ID ${externalId}`);
                return response.data.response[0];
            }

            this.logger.warn(`API returned no data for player ID ${externalId}`);
            return null;
        } catch (error) {
            this.logger.error(`Error fetching player from API: ${error.message}`);
            return null;
        }
    }

    /**
     * Save multiple players from API data in parallel batches
     */
    private async savePlayersFromApiBatch(apiPlayersData: any[]): Promise<void> {
        try {
            if (apiPlayersData.length === 0) return;

            this.logger.debug(`Starting batch save for ${apiPlayersData.length} players`);

            // Chia players thành batches nhỏ hơn để tránh timeout
            const batches = [];
            for (let i = 0; i < apiPlayersData.length; i += this.BATCH_SIZE) {
                batches.push(apiPlayersData.slice(i, i + this.BATCH_SIZE));
            }

            this.logger.debug(`Processing ${batches.length} batches of players`);

            // Process từng batch song song với Promise.all
            const saveResults = await Promise.all(
                batches.map(async (batch, index) => {
                    try {
                        const savedCount = await this.processBatchPlayers(batch);
                        this.logger.debug(`Batch ${index + 1}/${batches.length}: Saved ${savedCount}/${batch.length} players`);
                        return savedCount;
                    } catch (error) {
                        this.logger.error(`Failed to save batch ${index + 1}: ${error.message}`);
                        return 0;
                    }
                })
            );

            const totalSaved = saveResults.reduce((sum: number, count: number) => sum + count, 0);
            this.logger.debug(`Successfully saved ${totalSaved}/${apiPlayersData.length} players in ${batches.length} batches`);

        } catch (error) {
            this.logger.error(`Failed to save players batch: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Process a batch of players with upsert logic
     */
    private async processBatchPlayers(batchData: any[]): Promise<number> {
        try {
            const playersToSave: Player[] = [];
            const statisticsMap = new Map<number, any[]>(); // Map externalId to statistics

            // Prepare all players and map statistics
            for (const apiData of batchData) {
                const playerData = apiData.player;
                const statisticsData = apiData.statistics || [];

                // Download player photo if available
                const playerPhotoPath = playerData.photo
                    ? await this.imageService.downloadImage(playerData.photo, 'players', `${playerData.id}.png`)
                    : '';

                // Create player entity
                const player = this.playerRepository.create({
                    externalId: playerData.id,
                    name: playerData.name,
                    firstName: playerData.firstname,
                    lastName: playerData.lastname,
                    age: playerData.age,
                    birthDate: playerData.birth?.date ? new Date(playerData.birth.date) : undefined,
                    birthPlace: playerData.birth?.place,
                    birthCountry: playerData.birth?.country,
                    nationality: playerData.nationality,
                    height: playerData.height,
                    weight: playerData.weight,
                    injured: playerData.injured || false,
                    photo: playerPhotoPath,
                    timestamp: Date.now(),
                });

                playersToSave.push(player);

                // Store statistics for later processing
                if (statisticsData.length > 0) {
                    statisticsMap.set(playerData.id, statisticsData);
                }
            }

            // Upsert players first
            if (playersToSave.length > 0) {
                await this.playerRepository.upsert(playersToSave, ['externalId']);
            }

            // Process statistics if any
            if (statisticsMap.size > 0) {
                const externalIds = Array.from(statisticsMap.keys());
                const savedPlayers = await this.playerRepository.find({
                    where: { externalId: In(externalIds) }
                });

                // Create statistics with correct playerId
                const statisticsToSave: PlayerStatistics[] = [];

                for (const player of savedPlayers) {
                    const playerStats = statisticsMap.get(player.externalId);
                    if (playerStats) {
                        for (const stat of playerStats) {
                            // Download team logo if available
                            const teamLogoPath = stat.team?.logo
                                ? await this.imageService.downloadImage(stat.team.logo, 'teams', `${stat.team.id}.png`)
                                : '';

                            // Download league logo if available
                            const leagueLogoPath = stat.league?.logo
                                ? await this.imageService.downloadImage(stat.league.logo, 'leagues', `${stat.league.id}.png`)
                                : '';

                            const playerStat = this.playerStatisticsRepository.create({
                                playerId: player.id,
                                teamId: stat.team?.id,
                                teamName: stat.team?.name,
                                teamLogo: teamLogoPath,
                                leagueId: stat.league?.id,
                                leagueName: stat.league?.name,
                                leagueCountry: stat.league?.country,
                                leagueLogo: leagueLogoPath,
                                season: stat.league?.season,
                                games: stat.games,
                                substitutes: stat.substitutes,
                                shots: stat.shots,
                                goals: stat.goals,
                                passes: stat.passes,
                                tackles: stat.tackles,
                                duels: stat.duels,
                                dribbles: stat.dribbles,
                                fouls: stat.fouls,
                                cards: stat.cards,
                                penalty: stat.penalty,
                            });

                            statisticsToSave.push(playerStat);
                        }
                    }
                }

                // Upsert statistics
                if (statisticsToSave.length > 0) {
                    await this.playerStatisticsRepository.upsert(statisticsToSave, ['playerId', 'teamId', 'leagueId', 'season']);
                }
            }

            return playersToSave.length;

        } catch (error) {
            this.logger.error(`Error processing batch players: ${error.message}`, error.stack);
            throw error;
        }
    }



    /**
     * Save player from API data
     */
    private async savePlayerFromApi(apiData: any): Promise<Player> {
        try {
            const playerData = apiData.player;
            const statisticsData = apiData.statistics || [];

            // Create player entity
            const playerlogoPath = playerData.photo
                ? await this.imageService.downloadImage(playerData.photo, 'players', `${playerData.id}.png`)
                : '';
            const player = this.playerRepository.create({
                externalId: playerData.id,
                name: playerData.name,
                firstName: playerData.firstname,
                lastName: playerData.lastname,
                age: playerData.age,
                birthDate: playerData.birth?.date ? new Date(playerData.birth.date) : undefined,
                birthPlace: playerData.birth?.place,
                birthCountry: playerData.birth?.country,
                nationality: playerData.nationality,
                height: playerData.height,
                weight: playerData.weight,
                injured: playerData.injured || false,
                photo: playerlogoPath,
                timestamp: Date.now(),
            });

            // Upsert player (insert or update if exists)
            await this.playerRepository.upsert([player], ['externalId']);

            // Get the saved player with relations
            const savedPlayer = await this.playerRepository.findOne({
                where: { externalId: playerData.id },
                relations: ['statistics']
            });

            if (!savedPlayer) {
                throw new Error(`Failed to save/retrieve player with external ID ${playerData.id}`);
            }

            // Save statistics if available
            if (statisticsData.length > 0) {
                for (const stat of statisticsData) {
                    // Download team logo if available
                    const teamLogoPath = stat.team?.logo
                        ? await this.imageService.downloadImage(stat.team.logo, 'teams', `${stat.team.id}.png`)
                        : '';

                    // Download league logo if available
                    const leagueLogoPath = stat.league?.logo
                        ? await this.imageService.downloadImage(stat.league.logo, 'leagues', `${stat.league.id}.png`)
                        : '';

                    const playerStat = this.playerStatisticsRepository.create({
                        playerId: savedPlayer.id,
                        teamId: stat.team?.id,
                        teamName: stat.team?.name,
                        teamLogo: teamLogoPath,
                        leagueId: stat.league?.id,
                        leagueName: stat.league?.name,
                        leagueCountry: stat.league?.country,
                        leagueLogo: leagueLogoPath,
                        season: stat.league?.season,
                        games: stat.games,
                        substitutes: stat.substitutes,
                        shots: stat.shots,
                        goals: stat.goals,
                        passes: stat.passes,
                        tackles: stat.tackles,
                        duels: stat.duels,
                        dribbles: stat.dribbles,
                        fouls: stat.fouls,
                        cards: stat.cards,
                        penalty: stat.penalty,
                    });

                    // Use upsert for statistics as well to handle updates
                    await this.playerStatisticsRepository.upsert([playerStat], ['playerId', 'teamId', 'leagueId', 'season']);
                }
            }

            return savedPlayer;
        } catch (error) {
            this.logger.error(`Error saving player from API: ${error.message}`, error.stack);
            throw error;
        }
    }

    /**
     * Map player to response with statistics from database
     */
    private mapPlayerToResponse(player: Player): any {
        return {
            player: {
                id: player.externalId,
                name: player.name,
                firstname: player.firstName,
                lastname: player.lastName,
                age: player.age,
                birth: {
                    date: player.birthDate ? (player.birthDate instanceof Date ? player.birthDate.toISOString().split('T')[0] : String(player.birthDate)) : null,
                    place: player.birthPlace,
                    country: player.birthCountry
                },
                nationality: player.nationality,
                height: player.height,
                weight: player.weight,
                injured: player.injured,
                photo: player.photo || ''
            },
            statistics: (player.statistics || []).map(stat => ({
                team: {
                    id: stat.teamId,
                    name: stat.teamName,
                    logo: stat.teamLogo || ''
                },
                league: {
                    id: stat.leagueId,
                    name: stat.leagueName,
                    country: stat.leagueCountry,
                    logo: stat.leagueLogo || '',
                    flag: stat.leagueCountry || '',
                    season: stat.season
                },
                games: stat.games,
                substitutes: stat.substitutes,
                shots: stat.shots,
                goals: stat.goals,
                passes: stat.passes,
                tackles: stat.tackles,
                duels: stat.duels,
                dribbles: stat.dribbles,
                fouls: stat.fouls,
                cards: stat.cards,
                penalty: stat.penalty
            }))
        };
    }

    /**
     * Map player to detailed response with statistics
     */
    private mapPlayerToDetailedResponse(player: Player): PlayerResponseDto {
        return {

            player: {
                id: player.externalId,
                name: player.name,
                firstname: player.firstName,
                lastname: player.lastName,
                age: player.age,
                birth: {
                    date: player.birthDate ? (player.birthDate instanceof Date ? player.birthDate.toISOString().split('T')[0] : String(player.birthDate)) : null,
                    place: player.birthPlace,
                    country: player.birthCountry
                },
                nationality: player.nationality,
                height: player.height,
                weight: player.weight,
                injured: player.injured,
                photo: player.photo || '',
                statistics: []
            },
            statistics: (player.statistics || []).map(stat => ({
                team: {
                    id: stat.teamId,
                    name: stat.teamName,
                    logo: stat.teamLogo || ''
                },
                league: {
                    id: stat.leagueId,
                    name: stat.leagueName,
                    country: stat.leagueCountry,
                    logo: stat.leagueLogo || '',
                    flag: stat.leagueCountry || '',
                    season: stat.season
                },
                games: stat.games,
                substitutes: stat.substitutes,
                shots: stat.shots,
                goals: stat.goals,
                passes: stat.passes,
                tackles: stat.tackles,
                duels: stat.duels,
                dribbles: stat.dribbles,
                fouls: stat.fouls,
                cards: stat.cards,
                penalty: stat.penalty
            }))
        };
    }

    /**
     * Validate league and season parameters
     */
    private validateLeagueAndSeason(league: number, season: number): void {
        if (!league || league <= 0) {
            throw new Error('League ID must be a positive number');
        }

        if (!season || season <= 0) {
            throw new Error('Season must be a positive number');
        }

        const currentYear = new Date().getFullYear();
        if (season < 2000 || season > currentYear + 1) {
            throw new Error(`Season must be between 2000 and ${currentYear + 1}`);
        }
    }

    /**
     * Validate pagination parameters
     */
    private validatePagination(page: number, limit: number): void {
        if (!page || page <= 0) {
            throw new Error('Page must be a positive number');
        }

        if (!limit || limit <= 0) {
            throw new Error('Limit must be a positive number');
        }

        if (limit > 200) {
            throw new Error('Limit cannot exceed 50 items per page');
        }
    }

    /**
     * Validate external ID parameter
     */
    private validateExternalId(externalId: number): void {
        if (!externalId || externalId <= 0) {
            throw new Error('External ID must be a positive number');
        }
    }


}
